export const preprocessLaTeX = (content: string) => {
  if (!content) return ''

  const blockProcessedContent = content.replace(
    /\\\[(.*?)\\\]/g,
    (_, equation) => `$$${equation}$$`
  )

  const inlineProcessedContent = blockProcessedContent.replace(
    /\\\((.*?)\\\)/g,
    (_, equation) => `$${equation}$`
  )

  // Replace double newlines with double LaTeX line breaks or custom spacing
  const withParagraphs = inlineProcessedContent.replace(/\n\n/g, ' \\\\ \\\\ ')

  // Replace single newlines with LaTeX line break
  const withLineBreaks = withParagraphs.replace(/\n/g, ' \\\\ ')

  return withLineBreaks
}
