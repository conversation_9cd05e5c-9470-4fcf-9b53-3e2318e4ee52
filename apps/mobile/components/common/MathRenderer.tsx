import { useMemo, type FC } from 'react'
import { View } from 'react-native'
import Markdown from 'react-native-markdown-display'
import MathJax from 'react-native-mathjax-svg'
import MarkdownIt from 'markdown-it'
import mathjax3 from 'markdown-it-mathjax3'
import { WeightToFontFamily } from '@/lib'
import { FontWeight } from '@/types'

const md = new MarkdownIt()

md.use(mathjax3, {
  tex: {
    inlineMath: [
      ['$', '$'],
      ['\\(', '\\)']
    ],
    displayMath: [
      ['$$', '$$'],
      ['\\[', '\\]']
    ]
  }
})

function separateMathFromMd(input: string): string {
  const pattern = /(\*\*|\*)(?<content>[\s\S]*?)\1/g

  return input.replace(pattern, (fullMatch, delimiter, content: string) => {
    if (!content.includes('$')) {
      return fullMatch
    }

    const mathRegex = /\$(.+?)\$/g
    let lastIndex = 0
    let match
    const segments: string[] = []

    while ((match = mathRegex.exec(content)) !== null) {
      const mathStart = match.index
      const mathEnd = mathRegex.lastIndex

      if (mathStart > lastIndex) {
        segments.push(
          delimiter + content.slice(lastIndex, mathStart) + delimiter
        )
      }
      segments.push(match[0])
      lastIndex = mathEnd
    }

    if (lastIndex < content.length) {
      segments.push(delimiter + content.slice(lastIndex) + delimiter)
    }

    return segments.join('')
  })
}

export const MathRenderer: FC<{
  id: string
  children: string
  color: string
  fontWeight: FontWeight
  fontSize: number
}> = ({ id, children: text, color, fontWeight = '600', fontSize }) => {
  const transformedText = useMemo(() => {
    if (!text) return ''

    // First separate math parts
    const separateMath = separateMathFromMd(text.trim())

    // Process math markers and remove ALL \n
    return separateMath
      .replace(/\\\[((?:\\.|[\s\S])*?)\\\]/g, (_, inside) => `$$${inside}$$`)
      .replace(/\\\(((?:\\.|[\s\S])*?)\\\)/g, (_, inside) => `$${inside}$`)
      .replace(/\$\s+/g, '$')
      .replace(/\s+\$/g, '$')
      .replace(/\$\$\s+/g, '$$')
      .replace(/\s+\$\$/g, '$$')
      .replace(/\s*\$\$([\s\S]+?)\$\$\s*/g, ' $$$1$$ ')
      // Remove all newlines
      .replace(/\n+/g, ' ')
      .replace(/\s\s+/g, ' ') // clean up extra spaces
      .trim()
  }, [text])

  return (
    <Markdown
      markdownit={md}
      rules={{
        math_inline: (node) => (
          <MathJax
            key={`math_inline_${id}-${node.content}-${node.key}`}
            color={color}
            fontSize={fontSize}
            style={{ maxWidth: '100%' }}
          >
            {node.content}
          </MathJax>
        ),
        math_block: (node) => (
          <View
            key={`math_block_${id}-${node.content}-${node.key}`}
            style={{ marginVertical: 12 }}
          >
            <MathJax
              color={color}
              fontSize={fontSize}
              style={{ maxWidth: '100%' }}
            >
              {node.content}
            </MathJax>
          </View>
        )
      }}
      style={{
        body: {
          fontSize,
          color,
          fontWeight,
          fontFamily: WeightToFontFamily[fontWeight || '600']
        },
        text: {
          fontSize,
          color,
          fontWeight,
          fontFamily: WeightToFontFamily[fontWeight || '600']
        }
      }}
    >
      {transformedText}
    </Markdown>
  )
}
